{"metadata": {"version": "2.0.1", "created": "2025-05-25 10:29:59 UTC", "source": "/home/<USER>/private-repo/file-combiner/demo", "total_files": 2, "total_size": 38}, "files": [{"path": "config.json", "size": 17, "mtime": 1748168594.0897164, "mode": 33188, "encoding": "utf-8", "checksum": null, "mime_type": "application/json", "is_binary": false, "error": null, "ends_with_newline": true, "content": "{\"name\": \"demo\"}\n"}, {"path": "test.py", "size": 21, "mtime": 1748168594.0897164, "mode": 33188, "encoding": "utf-8", "checksum": null, "mime_type": "text/x-python", "is_binary": false, "error": null, "ends_with_newline": true, "content": "print(\"Hello World\")\n"}]}