#!/usr/bin/env python3
"""
Test script to verify the bug fixes in file_combiner.py
"""

import tempfile
import os
from pathlib import Path
import sys

# Add current directory to path
sys.path.insert(0, ".")

from file_combiner import FileCombiner, FileCombinerError


def test_max_workers_validation():
    """Test that negative max_workers values are handled correctly"""
    print("Testing max_workers validation...")

    # Test negative value
    combiner = FileCombiner({"max_workers": -5})
    assert (
        combiner.max_workers > 0
    ), "Negative max_workers should be converted to positive"

    # Test zero value
    combiner = FileCombiner({"max_workers": 0})
    assert combiner.max_workers > 0, "Zero max_workers should be converted to positive"

    # Test very large value
    combiner = File<PERSON>ombiner({"max_workers": 1000})
    assert combiner.max_workers <= 32, "Large max_workers should be capped at 32"

    print("✓ max_workers validation works correctly")


def test_temp_files_initialization():
    """Test that _temp_files is always initialized"""
    print("Testing _temp_files initialization...")

    combiner = File<PERSON>ombiner()
    assert hasattr(combiner, "_temp_files"), "_temp_files should be initialized"
    assert isinstance(combiner._temp_files, list), "_temp_files should be a list"

    # Test destructor doesn't crash
    del combiner

    print("✓ _temp_files initialization works correctly")


def test_content_preservation():
    """Test that file content is preserved correctly during combine/split"""
    print("Testing content preservation...")

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create test files with different characteristics
        test_files = {
            "normal.txt": "Hello World\nSecond line\nThird line",
            "no_newline.txt": "No trailing newline",
            "empty.txt": "",
            "with_hash.py": '#!/usr/bin/env python3\n# This is a comment\nprint("Hello")',
        }

        source_dir = temp_path / "source"
        source_dir.mkdir()

        for filename, content in test_files.items():
            (source_dir / filename).write_text(content)

        # Combine files
        combiner = FileCombiner({"verbose": False})
        archive_file = temp_path / "archive.txt"

        import asyncio

        success = asyncio.run(
            combiner.combine_files(source_dir, archive_file, progress=False)
        )
        assert success, "Combine should succeed"

        # Split files
        restored_dir = temp_path / "restored"
        success = asyncio.run(
            combiner.split_files(archive_file, restored_dir, progress=False)
        )
        assert success, "Split should succeed"

        # Verify content
        for filename, original_content in test_files.items():
            restored_file = restored_dir / filename
            assert restored_file.exists(), f"File {filename} should be restored"

            restored_content = restored_file.read_text()
            # Note: We expect line ending differences, so we normalize for comparison
            original_normalized = original_content.replace("\r\n", "\n")
            restored_normalized = restored_content.replace("\r\n", "\n")

            assert (
                original_normalized == restored_normalized
            ), f"Content of {filename} should match"

    print("✓ Content preservation works correctly")


def test_error_handling():
    """Test error handling improvements"""
    print("Testing error handling...")

    combiner = FileCombiner()

    # Test non-existent source
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        non_existent = temp_path / "does_not_exist"
        output_file = temp_path / "output.txt"

        import asyncio

        success = asyncio.run(
            combiner.combine_files(non_existent, output_file, progress=False)
        )
        assert not success, "Should return False for non-existent source"

    print("✓ Error handling works correctly")


def test_version_consistency():
    """Test that version numbers are consistent"""
    print("Testing version consistency...")

    from file_combiner import __version__

    # Check setup.py version
    with open("setup.py", "r") as f:
        setup_content = f.read()
        assert "2.0.1" in setup_content, "setup.py should have version 2.0.1"

    # Check file_combiner.py version
    assert __version__ == "2.0.1", "file_combiner.py should have version 2.0.1"

    print("✓ Version consistency works correctly")


def main():
    """Run all tests"""
    print("Running bug fix tests...\n")

    try:
        test_max_workers_validation()
        test_temp_files_initialization()
        test_content_preservation()
        test_error_handling()
        test_version_consistency()

        print("\n🎉 All tests passed! Bug fixes are working correctly.")
        return 0

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
