# Combined Files Archive
version: 2.0.1
created: '2025-05-25 10:29:59 UTC'
source: '/home/<USER>/private-repo/file-combiner/demo'
total_files: 2
total_size: 38

files:
  - path: 'config.json'
    size: 17
    mtime: 1748168594.0897164
    encoding: 'utf-8'
    is_binary: false
    content: |
      {"name": "demo"}
      

  - path: 'test.py'
    size: 21
    mtime: 1748168594.0897164
    encoding: 'utf-8'
    is_binary: false
    content: |
      print("Hello World")
      

