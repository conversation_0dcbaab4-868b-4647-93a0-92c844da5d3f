All notable changes to this project will be documented in this file.
The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).
- Complete Python rewrite for better performance
- Async I/O with parallel processing
- Advanced pattern matching with glob support
- Progress bars and detailed statistics
- Comprehensive test suite
- CI/CD with GitHub Actions
- Configuration file support
- Binary file handling with base64 encoding
- Cross-platform compatibility
- Dry run mode for previewing operations
- 10-50x performance improvement over bash version
- Better error handling and recovery
- More intuitive command-line interface
- Enhanced AI-friendly output format
- Robust Unicode support
- Moved from bash to Python implementation
- New archive format with JSON metadata
- Simplified installation via PyPI
- Initial bash implementation
- Basic combine and split operations
- Compression support
- Simple exclusion patterns
---
For older versions, see the git history.