<?xml version="1.0" encoding="UTF-8"?>
<file_archive version="2.0.1" created="2025-05-25 10:29:59 UTC" source="/home/<USER>/private-repo/file-combiner/demo" total_files="2" total_size="38">
  <file path="config.json" size="17" mtime="1748168594.0897164" mode="33188" encoding="utf-8" mime_type="application/json" is_binary="False" ends_with_newline="True">{"name": "demo"}
</file>
  <file path="test.py" size="21" mtime="1748168594.0897164" mode="33188" encoding="utf-8" mime_type="text/x-python" is_binary="False" ends_with_newline="True">print("Hello World")
</file>
</file_archive>