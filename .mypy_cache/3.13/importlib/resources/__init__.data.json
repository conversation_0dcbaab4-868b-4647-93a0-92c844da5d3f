{".class": "MypyFile", "_fullname": "importlib.resources", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AbstractContextManager": {".class": "SymbolTableNode", "cross_ref": "contextlib.AbstractContextManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Anchor": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._common.Anchor", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BinaryIO": {".class": "SymbolTableNode", "cross_ref": "typing.BinaryIO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Package": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._common.Package", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ResourceReader": {".class": "SymbolTableNode", "cross_ref": "importlib.abc.ResourceReader", "kind": "Gdef"}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Traversable": {".class": "SymbolTableNode", "cross_ref": "importlib.abc.Traversable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "importlib.resources.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "as_file": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._common.as_file", "kind": "Gdef"}, "contents": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._functional.contents", "kind": "Gdef"}, "files": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._common.files", "kind": "Gdef"}, "is_resource": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._functional.is_resource", "kind": "Gdef"}, "open_binary": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._functional.open_binary", "kind": "Gdef"}, "open_text": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._functional.open_text", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "path": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._functional.path", "kind": "Gdef"}, "read_binary": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._functional.read_binary", "kind": "Gdef"}, "read_text": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._functional.read_text", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/home/<USER>/.local/share/virtualenvs/athena-add-partitions-JInua3ct/lib/python3.13/site-packages/mypy/typeshed/stdlib/importlib/resources/__init__.pyi"}