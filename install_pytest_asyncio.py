#!/usr/bin/env python3
"""
Simple script to install pytest-asyncio without pip
"""
import subprocess
import sys
import os

def install_pytest_asyncio():
    """Install pytest-asyncio using alternative methods"""
    try:
        # Try using the system pip
        result = subprocess.run([sys.executable, "-m", "pip", "install", "pytest-asyncio"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("Successfully installed pytest-asyncio")
            return True
    except Exception as e:
        print(f"Failed to install with pip: {e}")
    
    try:
        # Try using easy_install
        result = subprocess.run(["easy_install", "pytest-asyncio"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("Successfully installed pytest-asyncio with easy_install")
            return True
    except Exception as e:
        print(f"Failed to install with easy_install: {e}")
    
    # Try downloading and installing manually
    try:
        import urllib.request
        import tempfile
        import zipfile
        import shutil
        
        print("Attempting manual installation...")
        
        # Download pytest-asyncio
        url = "https://files.pythonhosted.org/packages/source/p/pytest-asyncio/pytest-asyncio-0.21.1.tar.gz"
        with tempfile.TemporaryDirectory() as temp_dir:
            archive_path = os.path.join(temp_dir, "pytest-asyncio.tar.gz")
            urllib.request.urlretrieve(url, archive_path)
            
            # Extract and install
            import tarfile
            with tarfile.open(archive_path, 'r:gz') as tar:
                tar.extractall(temp_dir)
            
            # Find the extracted directory
            extracted_dir = None
            for item in os.listdir(temp_dir):
                if item.startswith('pytest-asyncio') and os.path.isdir(os.path.join(temp_dir, item)):
                    extracted_dir = os.path.join(temp_dir, item)
                    break
            
            if extracted_dir:
                # Install using setup.py
                old_cwd = os.getcwd()
                os.chdir(extracted_dir)
                result = subprocess.run([sys.executable, "setup.py", "install"], 
                                      capture_output=True, text=True)
                os.chdir(old_cwd)
                
                if result.returncode == 0:
                    print("Successfully installed pytest-asyncio manually")
                    return True
                else:
                    print(f"Manual installation failed: {result.stderr}")
    
    except Exception as e:
        print(f"Manual installation failed: {e}")
    
    print("All installation methods failed. Please install pytest-asyncio manually.")
    return False

if __name__ == "__main__":
    install_pytest_asyncio()
