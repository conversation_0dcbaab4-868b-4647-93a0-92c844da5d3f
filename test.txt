# Enhanced Combined Files Archive
# Generated by file-combiner v2.0.1
# Date: 2025-05-25 10:29:59 UTC
# Source: /home/<USER>/private-repo/file-combiner/demo
# Total files: 2
# Total size: 38.0B
#
# Format:
# === FILE_SEPARATOR ===
# FILE_METADATA: <json_metadata>
# ENCODING: <encoding_type>
# <file_content>
#

=== FILE_SEPARATOR ===
FILE_METADATA: {"path": "config.json", "size": 17, "mtime": 1748168594.0897164, "mode": 33188, "encoding": "utf-8", "checksum": null, "mime_type": "application/json", "is_binary": false, "error": null, "ends_with_newline": true}
ENCODING: utf-8
{"name": "demo"}

=== FILE_SEPARATOR ===
FILE_METADATA: {"path": "test.py", "size": 21, "mtime": 1748168594.0897164, "mode": 33188, "encoding": "utf-8", "checksum": null, "mime_type": "text/x-python", "is_binary": false, "error": null, "ends_with_newline": true}
ENCODING: utf-8
print("Hello World")

